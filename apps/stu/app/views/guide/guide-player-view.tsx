"use client";
import { toast } from "@/app/components/common/toast";
import { useClientContext } from "@/app/providers/client-provider";
import { GuidePlayerView as CoreGuidePlayerView } from "@repo/core/components/player/guide-player-view";
import {
  extractPlayerInputProps,
  extractVolcengineConfig,
  useGuideViewContextAdapter,
} from "@repo/core/components/player/guide-player-view/use-guide-view-context-adapter";
import { Guide } from "@repo/core/guide/guide";
import { Reference } from "@repo/core/types/data/comment";
import { GuideTheme } from "@repo/core/types/data/widget-guide";
import { FC } from "react";
import { useGuideViewContext } from "./guide-view-context";

interface GuidePlayerViewProps {
  className?: string;
  theme?: GuideTheme;
}

export const GuidePlayerView: FC<GuidePlayerViewProps> = ({
  className,
  theme,
}) => {
  const { studentUserInfo, screen } = useClientContext();
  const guideViewContext = useGuideViewContext();

  // 使用适配器将 context 转换为新组件需要的接口
  const controls = useGuideViewContextAdapter({
    ...guideViewContext,
    trackEventWithLessonId: guideViewContext.trackEventWithLessonId,
    showToast: (message: string) => toast.show(message),
  });

  // 提取 Player 组件的 inputProps
  const playerProps = extractPlayerInputProps(guideViewContext, {
    referenceList: guideViewContext.referenceList as Reference[],
  });

  // 提取 VolcengineVideo 的配置
  const volcengineConfig = extractVolcengineConfig(
    guideViewContext,
    studentUserInfo || undefined
  );

  if (!guideViewContext.data) {
    return <div>无数据</div>;
  }

  return (
    <CoreGuidePlayerView
      className={className}
      theme={theme}
      controls={controls}
      component={Guide}
      inputProps={
        {
          ...playerProps,
          fps: guideViewContext.data.avatar?.fps,
        } as any
      }
      screen={screen}
      onLineClick={(frame: number) => {
        guideViewContext.goto?.(frame);
      }}
      onScrollNext={(index: number) => {
        guideViewContext.goto?.(index);
      }}
      active={guideViewContext.active}
      volcengineConfig={volcengineConfig}
    />
  );
};
