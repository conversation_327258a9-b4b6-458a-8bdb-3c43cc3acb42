# GuidePlayerView 通用组件

这是一个从学生端应用提取出来的通用 Guide 播放器视图组件，可以在各个项目中复用。

## 功能特性

- 🎮 **播放控制**: 支持播放/暂停、跳转、倍速播放等功能
- 🖱️ **手势支持**: 支持双击播放/暂停、长按快进等手势操作
- 🎨 **主题支持**: 支持自定义主题配置
- 🔧 **高度可配置**: Player 组件和 inputProps 可从外部传入
- 📱 **响应式**: 支持不同屏幕尺寸
- 🎥 **视频集成**: 支持 VolcengineVideo 集成

## 使用方法

### 基本用法

```tsx
import { GuidePlayerView } from "@repo/core/components/player/guide-player-view";
import { Guide } from "@repo/core/guide/guide";

function MyGuidePlayer() {
  // 实现播放控制接口
  const controls = {
    isPlaying: false,
    canPlay: true,
    playRate: 1,
    showPlayerControls: false,
    togglePlay: () => {},
    seekTo: (frame: number) => {},
    // ... 其他必需的方法
  };

  return (
    <GuidePlayerView
      controls={controls}
      component={Guide}
      inputProps={{
        data: guideData,
        fps: 24,
        // ... 其他 inputProps
      }}
      screen={{ width: 1920, height: 1080 }}
      active={true}
    />
  );
}
```

### 使用适配器 (推荐用于现有项目迁移)

```tsx
import { 
  GuidePlayerView,
  useGuideViewContextAdapter,
  extractPlayerInputProps,
  extractVolcengineConfig
} from "@repo/core/components/player/guide-player-view";

function ExistingGuidePlayer() {
  const guideViewContext = useGuideViewContext();
  
  // 使用适配器转换现有的 context
  const controls = useGuideViewContextAdapter({
    ...guideViewContext,
    trackEventWithLessonId: guideViewContext.trackEventWithLessonId,
    showToast: (message: string) => toast.show(message),
  });

  // 提取 Player 组件的 inputProps
  const playerProps = extractPlayerInputProps(guideViewContext);

  // 提取 VolcengineVideo 的配置
  const volcengineConfig = extractVolcengineConfig(guideViewContext, userInfo);

  return (
    <GuidePlayerView
      controls={controls}
      component={Guide}
      inputProps={{
        ...playerProps,
        fps: guideViewContext.data.avatar?.fps,
      }}
      screen={screen}
      active={guideViewContext.active}
      volcengineConfig={volcengineConfig}
    />
  );
}
```

## API 参考

### GuidePlayerViewProps

| 属性 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `controls` | `GuidePlayerControlsInterface` | ✅ | 播放控制接口 |
| `component` | `React.ComponentType<any>` | ✅ | 传给 Player 的 component 参数 |
| `inputProps` | `GuidePlayerInputProps` | ✅ | 传给 Player 的 inputProps |
| `screen` | `{width: number, height: number}` | ✅ | 屏幕配置 |
| `active` | `boolean` | ✅ | 是否激活状态 |
| `className` | `string` | ❌ | 自定义样式类 |
| `theme` | `GuideTheme` | ❌ | 主题配置 |
| `onLineClick` | `(frame: number) => void` | ❌ | 行点击回调 |
| `onScrollNext` | `(index: number) => void` | ❌ | 滚动翻页回调 |
| `volcengineConfig` | `VolcengineVideoConfig` | ❌ | VolcengineVideo 配置 |

### GuidePlayerInputProps

传给 Player 组件的 inputProps 类型定义，具有严格的类型约束。

```tsx
interface GuidePlayerInputProps {
  client?: "stu" | "aipt" | "tch" | "";
  guideMode?: GuideMode;
  title?: string;
  index?: number;
  totalGuideCount?: number;
  data: GuideWidgetData; // 必需字段
  theme?: GuideTheme;
  showSubtitle?: boolean;
  onLineClick?: (frame: number) => void;
  refContainer?: RefObject<HTMLDivElement | null>;
  commentRef?: Signal<HTMLDivElement | null>;
  lineIdInRange?: string;
  referenceList?: Reference[];
  onClickReference?: (reference: MergedReference[string][string][number]) => void;
  onScrollFlip?: (index: number) => void;
  fps?: number;
}
```

### GuidePlayerControlsInterface

播放控制接口，各个应用需要实现这个接口来提供播放控制功能。

```tsx
interface GuidePlayerControlsInterface {
  // 播放状态
  isPlaying: boolean;
  canPlay: boolean;
  playRate: number;
  showPlayerControls: boolean;

  // 播放控制方法
  togglePlay: () => void;
  seekTo: (frame: number) => void;
  forwardTo: (seconds: number) => void;
  set3XPlayRate: () => void;
  resetPlayRate: () => void;
  togglePlayerControls: () => void;
  hidePlayerControls: () => void;
  setFollowMode: () => void;

  // 播放器引用
  refPlayer: RefObject<PlayerRef | null>;
  refVolcenginePlayer: Signal<VolcenginePlayer | null>;

  // 进度和配置
  progress: { frame: number };
  durationInFrames: number;

  // 容器引用
  refContainer: RefObject<HTMLDivElement | null>;

  // 事件回调 (可选)
  onTrackEvent?: (eventName: string) => void;
  onShowToast?: (message: string) => void;
}
```

## 迁移指南

如果你有现有的 GuidePlayerView 组件需要迁移到这个通用版本：

1. **安装依赖**: 确保 `packages/core` 已安装 `use-double-tap` 和 `use-long-press`
2. **使用适配器**: 使用 `useGuideViewContextAdapter` 来转换现有的 context
3. **更新导入**: 从 `@repo/core/components/player/guide-player-view` 导入组件
4. **测试功能**: 确保所有播放控制功能正常工作

## 注意事项

- 这个组件保留了原有的播放控制逻辑和手势处理
- 组件内部使用 Remotion 的 Player 组件，只需要从外部传入 `component` 和 `inputProps`
- 适配器函数帮助现有项目更容易地迁移到新组件
- 组件内部使用了 `useSignal` 和相关的手势库，确保依赖已正确安装
- `inputProps` 中需要包含 `fps` 属性，用于播放器配置
- **类型安全**: 使用了严格的 TypeScript 类型定义，减少了 `any` 的使用：
  - `GuidePlayerInputProps` 提供了完整的 inputProps 类型约束
  - `GuidePlayerControlsInterface` 定义了播放控制的标准接口
  - `VolcengineVideoConfig` 提供了视频配置的类型安全
  - `GestureHandlers` 定义了手势处理的类型
- **兼容性**: `component` 参数保持 `React.ComponentType<any>` 以兼容 Remotion Player 的要求
