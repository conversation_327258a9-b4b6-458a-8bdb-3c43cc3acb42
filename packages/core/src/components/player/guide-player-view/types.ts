import { Signal } from "@preact-signals/safe-react";
import { PlayerRef } from "@remotion/player";
import { RefObject } from "react";
import { MergedReference, Reference } from "../../../types/data/comment";
import {
  GuideMode,
  GuideTheme,
  GuideWidgetData,
} from "../../../types/data/widget-guide";
import { VolcenginePlayer } from "../../volcengine-video/volcengine-video";

/**
 * Guide Player 的 inputProps 类型
 */
export interface GuidePlayerInputProps {
  client?: "stu" | "aipt" | "tch" | "";
  guideMode?: GuideMode;
  title?: string;
  index?: number;
  totalGuideCount?: number;
  data: GuideWidgetData; // 必需字段，与 GuideProviderProps 保持一致
  theme?: GuideTheme;
  showSubtitle?: boolean;
  onLineClick?: (frame: number) => void;
  refContainer?: RefObject<HTMLDivElement | null>;
  commentRef?: Signal<HTMLDivElement | null>;
  lineIdInRange?: string;
  referenceList?: Reference[];
  onClickReference?: (
    reference: MergedReference[string][string][number]
  ) => void;
  onScrollFlip?: (index: number) => void;
  fps?: number;
}

/**
 * VolcengineVideo 配置类型
 */
export interface VolcengineVideoConfig {
  src: string;
  userId?: string;
  tag?: string;
}

/**
 * 播放控制接口 - 各个应用需要实现这个接口来提供播放控制功能
 */
export interface GuidePlayerControlsInterface {
  // 播放状态
  isPlaying: boolean;
  canPlay: boolean;
  playRate: number;
  showPlayerControls: boolean;

  // 播放控制方法
  togglePlay: () => void;
  seekTo: (frame: number) => void;
  forwardTo: (seconds: number) => void;
  set3XPlayRate: () => void;
  resetPlayRate: () => void;
  togglePlayerControls: () => void;
  hidePlayerControls: () => void;
  setFollowMode: () => void;

  // 播放器引用
  refPlayer: RefObject<PlayerRef | null>;
  refVolcenginePlayer: Signal<VolcenginePlayer | null>;

  // 进度和配置
  progress: { frame: number };
  durationInFrames: number;

  // 容器引用
  refContainer: RefObject<HTMLDivElement | null>;

  // 事件回调 (可选)
  onTrackEvent?: (eventName: string) => void;
  onShowToast?: (message: string) => void;
}

/**
 * GuidePlayerView 组件的 Props
 */
export interface GuidePlayerViewProps {
  className?: string;
  theme?: GuideTheme;

  // 播放控制接口
  controls: GuidePlayerControlsInterface;

  // 传给 Player 组件的 component 参数
  component: React.ComponentType<any>;

  // 传给 Player 组件的 inputProps
  inputProps: GuidePlayerInputProps;

  // 屏幕配置
  screen: {
    width: number;
    height: number;
  };

  // 事件回调
  onLineClick?: (frame: number) => void;
  onScrollNext?: (index: number) => void;

  // 是否激活状态
  active: boolean;

  // VolcengineVideo 相关配置
  volcengineConfig?: VolcengineVideoConfig;
}

/**
 * 手势处理相关的类型
 */
export interface GestureHandlers {
  doubleTapHandlers: {
    onTouchStart?: (e: React.TouchEvent) => void;
    onTouchEnd?: (e: React.TouchEvent) => void;
    onMouseDown?: (e: React.MouseEvent) => void;
    onMouseUp?: (e: React.MouseEvent) => void;
  };
  longPressHandlers: () => {
    onTouchStart?: (e: React.TouchEvent) => void;
    onTouchEnd?: (e: React.TouchEvent) => void;
    onMouseDown?: (e: React.MouseEvent) => void;
    onMouseUp?: (e: React.MouseEvent) => void;
  };
}
