"use client";
import { useSignal } from "@preact-signals/safe-react";
import { cn } from "@repo/ui/lib/utils";
import dynamic from "next/dynamic";
import { FC, useCallback, useEffect } from "react";
import { useDoubleTap } from "use-double-tap";
import { LongPressEventType, useLongPress } from "use-long-press";
import { GuidePlayerViewProps } from "./types";

const VolcengineVideo = dynamic(
  () => import("../../volcengine-video/volcengine-video"),
  {
    ssr: false,
  }
);

/**
 * 通用的 Guide 播放器视图组件
 *
 * 这个组件提供了播放器的基础 UI 和手势处理逻辑，
 * 具体的播放控制逻辑通过 controls 接口传入
 */
export const GuidePlayerView: FC<GuidePlayerViewProps> = ({
  className,
  theme,
  controls,
  PlayerComponent,
  playerProps,
  screen,
  onLineClick,
  onScrollNext,
  active,
  volcengineConfig,
}) => {
  const {
    playRate,
    refPlayer,
    refVolcenginePlayer,
    progress,
    durationInFrames,
    togglePlay,
    seekTo,
    togglePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    setFollowMode,
    onTrackEvent,
    onShowToast,
  } = controls;

  const showLastProgressToast = useSignal(false);
  const isComment = useSignal(false);

  const longPressHandlers = useLongPress(
    (e: any) => {
      // 检查是否点击在评论区域
      const doms = document.querySelectorAll("[data-name=line-container]");
      if (
        Array.from(doms).some((dom) => dom.contains(e.target as HTMLElement))
      ) {
        isComment.value = true;
        return;
      }
      set3XPlayRate();
    },
    {
      onFinish: () => {
        if (isComment.value) {
          isComment.value = false;
          return;
        }
        resetPlayRate();
        onTrackEvent?.("doc_fast_forward_longpress");
      },
      detect: LongPressEventType.Touch,
    }
  );

  const handleClick = useCallback(() => {
    togglePlayerControls();
  }, [togglePlayerControls]);

  const handleDoubleClick = useCallback(() => {
    togglePlay();
    onTrackEvent?.("doc_play_pause_doubleclick");
  }, [togglePlay, onTrackEvent]);

  const doubleTapHandlers = useDoubleTap(handleDoubleClick, 300, {
    onSingleTap: handleClick,
  });

  const handleLineClick = useCallback(
    (frame: number) => {
      seekTo(frame);
      setFollowMode();
      onTrackEvent?.("doc_learn_from_here_click");
      onLineClick?.(frame);
    },
    [seekTo, setFollowMode, onTrackEvent, onLineClick]
  );

  const handleScrollNext = useCallback(
    (index: number) => {
      onScrollNext?.(index);
    },
    [onScrollNext]
  );

  // 显示进度提示
  useEffect(() => {
    if (!active) return;
    if (showLastProgressToast.value === true) return;
    if (progress.frame > 0) {
      onShowToast?.("已从上次进度开始学习");
    }
    showLastProgressToast.value = true;
  }, [progress, showLastProgressToast, active, onShowToast]);

  // 构建传给 Player 组件的完整 props
  const fullPlayerProps = {
    ...playerProps,
    onLineClick: handleLineClick,
    onScrollFlip: handleScrollNext,
    theme,
  };

  return (
    <div
      {...doubleTapHandlers}
      data-name="guide-player"
      className="relative h-screen w-full"
      {...longPressHandlers()}
    >
      <PlayerComponent
        ref={refPlayer}
        className={cn("h-full w-full", className)}
        style={{ height: screen.height }}
        inputProps={fullPlayerProps}
        initialFrame={progress.frame}
        durationInFrames={durationInFrames}
        playbackRate={playRate}
        allowFullscreen={false}
        compositionWidth={screen.width}
        compositionHeight={screen.height}
        acknowledgeRemotionLicense
        errorFallback={(e: { error: { message: string } }) => (
          <span className="text-sm text-red-500">错误: {e.error.message}</span>
        )}
      />
      {active && volcengineConfig && (
        <div className="max-w-1/5 pointer-events-none absolute bottom-0 right-0 z-50 w-[calc(100%-var(--width-guide))]">
          <VolcengineVideo
            ref={refVolcenginePlayer}
            className="relative flex h-full w-full flex-col items-center justify-end"
            src={volcengineConfig.src}
            startTime={progress.frame / (playerProps.fps || 24)}
            playRate={playRate}
            userId={volcengineConfig.userId}
            tag={volcengineConfig.tag || "文稿组件"}
          />
        </div>
      )}
    </div>
  );
};
