import { useMemo } from "react";
import { GuidePlayerControlsInterface } from "./types";

/**
 * 简化的适配器 hook，用于将现有的 GuideViewContext 转换为 GuidePlayerControlsInterface
 *
 * 使用 any 类型以简化迁移过程，避免复杂的类型检查
 * 各个应用可以根据自己的需求传入相应的 context
 * 支持可选功能，不是所有项目都需要实现所有播放控制功能
 */
export function useGuideViewContextAdapter(
  context: any
): GuidePlayerControlsInterface {
  return useMemo(
    () => ({
      // 必需的播放状态
      isPlaying: <PERSON><PERSON>an(context.isPlaying),
      canPlay: <PERSON><PERSON>an(context.canPlay),
      playRate:
        typeof context.playRate === "object"
          ? context.playRate?.value || 1
          : context.playRate || 1,

      // 必需的播放控制方法
      togglePlay: context.togglePlay || (() => {}),
      seekTo: context.seekTo || (() => {}),

      // 必需的播放器引用
      refPlayer: context.refPlayer || { current: null },
      refVolcenginePlayer: context.refVolcenginePlayer || { value: null },

      // 必需的进度和配置
      progress: context.progress || { frame: 0 },
      durationInFrames: context.durationInFrames || 0,

      // 必需的容器引用
      refContainer: context.refContainer || { current: null },

      // 可选的播放控制功能
      ...(context.showPlayerControls !== undefined && {
        showPlayerControls: context.showPlayerControls,
      }),
      ...(context.forwardTo && { forwardTo: context.forwardTo }),
      ...(context.set3XPlayRate && { set3XPlayRate: context.set3XPlayRate }),
      ...(context.resetPlayRate && { resetPlayRate: context.resetPlayRate }),
      ...(context.togglePlayerControls && {
        togglePlayerControls: context.togglePlayerControls,
      }),
      ...(context.hidePlayerControls && {
        hidePlayerControls: context.hidePlayerControls,
      }),
      ...(context.setFollowMode && { setFollowMode: context.setFollowMode }),

      // 可选的事件回调
      ...(context.trackEventWithLessonId && {
        onTrackEvent: context.trackEventWithLessonId,
      }),
      ...(context.showToast && { onShowToast: context.showToast }),
    }),
    [context]
  );
}

/**
 * 简化的 inputProps 提取函数
 * 使用 any 类型以简化迁移过程
 */
export function extractPlayerInputProps(
  context: Record<string, unknown>,
  additionalProps: Record<string, unknown> = {}
) {
  // 安全地提取 guideMode
  const guideMode =
    context.guideMode &&
    typeof context.guideMode === "object" &&
    "value" in context.guideMode
      ? (context.guideMode as { value: unknown }).value
      : context.guideMode;

  return {
    client: "stu" as const,
    guideMode,
    title: context.title,
    index: context.index,
    totalGuideCount: context.totalGuideCount,
    data: context.data,
    showSubtitle: context.showSubtitle,
    refContainer: context.refContainer,
    commentRef: context.commentRef,
    lineIdInRange: (
      context.ranges as Array<{ lineId?: string }> | undefined
    )?.[0]?.lineId,
    referenceList: context.referenceList,
    onClickReference: context.onClickReference,
    fps: (context.data as { avatar?: { fps?: number } } | undefined)?.avatar
      ?.fps,
    ...additionalProps,
  };
}

/**
 * 简化的 VolcengineVideo 配置提取函数
 */
export function extractVolcengineConfig(
  context: Record<string, unknown>,
  userInfo?: Record<string, unknown>
) {
  const data = context.data as { avatar?: { url?: string } } | undefined;
  const avatarUrl = data?.avatar?.url;

  if (!avatarUrl || typeof avatarUrl !== "string") {
    return undefined;
  }

  return {
    src: avatarUrl,
    userId: userInfo?.userId as string | undefined,
    tag: "文稿组件",
  };
}
